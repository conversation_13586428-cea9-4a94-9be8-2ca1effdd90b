class Product < ApplicationRecord
  belongs_to :user
  has_many :cart_items, dependent: :destroy
  has_many :carts, through: :cart_items

  mount_uploader :image, ImageUploader
  serialize :image, JSON # If you use SQLite, add this line

  validates :title, :brand, :price, :model, presence: true
  validates :description, presence: true, length: { maximum: 1000, too_long: "%{count} characters is the maximum allowed. "}
  validates :title, length: { maximum: 140, too_long: "%{count} characters is the maximum allowed. "}
  validates :price, numericality: { greater_than: 0 }
  validates :brand, inclusion: { in: BRAND, message: "%{value} is not a valid brand" }
  validates :finish, inclusion: { in: FINISH, message: "%{value} is not a valid finish" }, allow_blank: true
  validates :condition, inclusion: { in: CONDITION, message: "%{value} is not a valid condition" }, allow_blank: true

  # You can input more brands finish and condition here
  BRAND = %w{ Ferrari Opel Lenovo Fossil Apple Samsung Sony Nike Adidas Toyota Honda Other}
  FINISH = %w{ Black White Navy Blue Red Clear Satin Yellow Seafoam Silver Gold }
  CONDITION = %w{ New Excellent Mint Used Fair Poor }

  # Available options for dropdowns (aliases for consistency)
  BRANDS = BRAND
  COLORS = FINISH
  CONDITIONS = CONDITION

  scope :by_brand, ->(brand) { where(brand: brand) if brand.present? }
  scope :by_condition, ->(condition) { where(condition: condition) if condition.present? }
  scope :by_user, ->(user) { where(user: user) if user.present? }

  # Handle image URL with fallback for missing images
  def image_url(version = nil)
    if image.present?
      case version
      when :thumb
        image.thumb.url
      when :default
        image.default.url
      else
        image.url
      end
    else
      # Fallback to placeholder image
      case version
      when :thumb
        "https://via.placeholder.com/400x300?text=No+Image"
      when :default
        "https://via.placeholder.com/800x600?text=No+Image"
      else
        "https://via.placeholder.com/400x300?text=No+Image"
      end
    end
  end
end
