class ProductsController < ApplicationController
  include CurrentCart

  before_action :set_product, only: [ :show, :edit, :update, :destroy ]
  before_action :authenticate_user!, except: [:index, :show]
  before_action :check_owner, only: [:edit, :update, :destroy]
  before_action :set_cart, only: [:index, :show]


  # GET /products or /products.json
  def index
    @products = Product.all.order("created_at desc")
  end

  # GET /products/1 or /products/1.json
  def show
    # @product is already set by the set_product before_action
  end

  def find_product_by_id(id)
    connection.execute("SELECT * FROM products WHERE products.id = ? LIMIT 1", id).first
  end

  # GET /products/new
  def new
    @product = current_user.products.build
  end

  # GET /products/1/edit
  def edit
  end

  # POST /products or /products.json
  def create
    @product = current_user.products.build(product_params)

    respond_to do |format|
      if @product.save
        format.html { redirect_to @product, notice: "Product was successfully created." }
        format.json { render :show, status: :created, location: @product }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @product.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /products/1 or /products/1.json
  def update
    respond_to do |format|
      if @product.update(product_params)
        format.html { redirect_to @product, notice: "Product was successfully updated." }
        format.json { render :show, status: :ok, location: @product }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @product.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /products/1 or /products/1.json
  def destroy
    @product.destroy
    respond_to do |format|
      format.html { redirect_to products_url, notice: "Product was successfully deleted." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_product
      @product = Product.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def product_params
      params.require(:product).permit(:brand, :model, :description, :condition, :finish, :title, :price, :image)
    end

    # Check if current user owns the product
    def check_owner
      redirect_to products_path, alert: 'Not authorized to perform this action.' unless @product.user == current_user
    end
end
