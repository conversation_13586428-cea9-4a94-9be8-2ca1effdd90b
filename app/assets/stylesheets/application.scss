/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS and SCSS file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS/SCSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

$link: #444;
$link-hover: #222;

@import "bulma";
@import "functions";


.notification {
  border-radius: 0;
}

.notification:not(:last-child) {
  margin-bottom: 0;
}

.product-index-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));

  .product {
    border-left: 0;
    border-top: 0;
    position: relative;
    min-height: 375px;

    .title {
      line-height: 1.6rem;
    }
    .price {
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
  }

  .product-thumb {
    position: relative;
    max-height: 150px;
    overflow: hidden;

    img {
      max-width: 100%;
      width: 100%;
      &:hover {
        opacity: .9;
      }
    }

    .condition {
      position: absolute;
      bottom: 10px;
      right: 9px;
    }
  }
}