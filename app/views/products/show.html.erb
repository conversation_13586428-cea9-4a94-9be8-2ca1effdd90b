<% content_for :body_class, 'bg-light' %>

<section class="section product-show">
  <div class="columns">
    <div class="column is-8">
      <h1 class="title is-2"><%= @product.title %></h1>

      <ul class="pv1">
        <% if @product.brand? %>
        <li class="inline-block pr3"><%= @product.brand %></li>
        <% end %>

        <li class="inline-block pr3"><%= @product.model %></li>

        <% if @product.condition? %>
        <li class="inline-block pr3"><%= @product.condition %></li>
        <% end %>
      </ul>

      <div class="feature-image">
        <%= image_tag(@product.image_url(:default)) %>
      </div>

      <div class="content pa4 mt3 bg-white border-radius-3">

      <h3 class="subtitle is-4">Description</h3>
      <%= @product.description %>


      <h3 class="subtitle is-4 pt5">Product Specs</h3>

      <table class="table is-narrow">
        <% if @product.condition %>
        <tr>
          <td class="has-text-weight-bold">Condition:</td>
          <td><%= @product.condition %></td>
        </tr>
        <% end %>

        <% if @product.finish %>
          <tr>
            <td class="has-text-weight-bold">Finish:</td>
            <td><%= @product.finish %></td>
          </tr>
        <% end %>

        <% if @product.brand %>
          <tr>
            <td class="has-text-weight-bold">Brand:</td>
            <td><%= @product.brand %></td>
          </tr>
        <% end %>

        <tr>
          <td class="has-text-weight-bold">Model:</td>
          <td><%= @product.model %></td>
        </tr>
      </table>
    </div>
    </div>
    <div class="column is-3 is-offset-1">
      <div class="bg-white pa4 border-radius-3">
        <h4 class="title is-5 has-text-centered"><%= number_to_currency(@product.price) %></h4>
        <p class="has-text-centered mb4">Sold by <%= @product.user&.name || "Unknown Seller" %></p>

        <div class="has-text-centered">
          <% if user_signed_in? && current_user == @product.user %>
            <!-- Owner view - show ad management options -->
            <div class="notification is-info is-light mb3">
              <p class="has-text-weight-bold">This is your ad</p>
            </div>
            <div class="field is-grouped">
              <div class="control is-expanded">
                <%= link_to "Edit Ad", edit_product_path(@product), class: "button is-info is-fullwidth" %>
              </div>
              <div class="control is-expanded">
                <%= link_to "Delete Ad", product_path(@product), method: :delete,
                           confirm: "Are you sure you want to delete this ad?", class: "button is-danger is-fullwidth" %>
              </div>
            </div>
            <div class="mt3">
              <%= link_to "View All My Ads", my_ads_products_path, class: "button is-light is-fullwidth" %>
            </div>
          <% else %>
            <!-- Buyer view - show purchase options -->
            <%= form_with url: cart_add_item_path, method: :post, local: true do |form| %>
              <%= form.hidden_field :product_id, value: @product.id %>
              <%= form.submit "Add to Cart", class: "button is-primary is-large is-fullwidth mb3" %>
            <% end %>
            <p class="has-text-grey is-size-7">Contact seller for more details</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>

</section>