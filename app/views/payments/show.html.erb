<div class="columns is-centered">
  <div class="column is-two-thirds">
    <h1 class="title">Payment Details</h1>
    
    <div class="box">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <div>
              <p class="heading">Payment ID</p>
              <p class="title is-6">#<%= @payment.id %></p>
            </div>
          </div>
          <div class="level-item">
            <div>
              <p class="heading">Status</p>
              <span class="tag <%= @payment.succeeded? ? 'is-success' : @payment.failed? ? 'is-danger' : 'is-warning' %>">
                <%= @payment.status.humanize %>
              </span>
            </div>
          </div>
          <div class="level-item">
            <div>
              <p class="heading">Amount</p>
              <p class="title is-6">$<%= @payment.amount %></p>
            </div>
          </div>
          <div class="level-item">
            <div>
              <p class="heading">Date</p>
              <p class="title is-6"><%= @payment.created_at.strftime("%B %d, %Y") %></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <% if @payment.cart.cart_items.any? %>
      <div class="box">
        <h2 class="title is-4">Items Purchased</h2>
        
        <% @payment.cart.items_with_products.each do |item| %>
          <div class="media">
            <figure class="media-left">
              <p class="image is-64x64">
                <% if item.product.image.present? %>
                  <%= image_tag item.product.image.url(:thumb), alt: item.product.title %>
                <% else %>
                  <img src="https://via.placeholder.com/64x64?text=No+Image" alt="<%= item.product.title %>">
                <% end %>
              </p>
            </figure>
            
            <div class="media-content">
              <div class="content">
                <p>
                  <strong><%= link_to item.product.title, product_path(item.product) %></strong>
                  <br>
                  <small>by <%= seller_name(item.product) %></small>
                  <br>
                  $<%= item.product.price %> × <%= item.quantity %> = 
                  <strong>$<%= item.total_price %></strong>
                </p>
              </div>
            </div>
          </div>
          
          <% unless item == @payment.cart.items_with_products.last %>
            <hr>
          <% end %>
        <% end %>
      </div>
    <% end %>
    
    <div class="has-text-centered">
      <%= link_to "← Back to Products", products_path, class: "button is-light" %>
    </div>
  </div>
</div>
