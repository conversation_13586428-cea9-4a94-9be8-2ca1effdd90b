<!DOCTYPE html>
<html>
  <head>
    <title>Shop</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag 'application', media: 'all', 'data-turbolinks-track': 'reload' %>
    <%= javascript_include_tag 'application', 'data-turbo-track': 'reload', defer: true %>

  </head>

  <body>
   <body class="<%= yield (:body_class) %>">
    <% if flash[:notice] %>
      <div class="notification is-success global-notification">
        <p class="notice"><%= notice %></p>
      </div>
    <% end %>
    <% if flash[:alert] %>
    <div class="notification is-danger global-notification">
      <p class="alert"><%= alert %></p>
    </div>
    <% end %>
     <nav class="navbar is-warning" role="navigation" aria-label="main navigation">
      <div class="navbar-brand">
        <%= link_to root_path, class:"navbar-item" do %>
          <h1 class="title is-5">Shop</h1>
        <% end  %>
      <div class="navbar-burger burger" data-target="navbar">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>

      <div id="navbar" class="navbar-menu">
        <div class="navbar-end">
          <div class="navbar-item">
            <div class="field is-grouped">

            <!-- Cart Icon -->
            <div class="navbar-item">
              <%= link_to cart_path, class: "button is-light" do %>
                🛒 Cart
                <span class="tag is-dark ml-1">
                  <% if defined?(@cart) && @cart %>
                    <%= @cart.total_items %>
                  <% else %>
                    0
                  <% end %>
                </span>
              <% end %>
            </div>

            <% if user_signed_in? %>
              <%= link_to 'Sell', new_product_path, class: "navbar-item button is-dark" %>

              <div class="navbar-item has-dropdown is-hoverable">
                <%= link_to 'Account', edit_user_registration_path, class: "navbar-link" %>
                <div class="navbar-dropdown is-right">
                  <%= link_to current_user.name, edit_user_registration_path, class:"navbar-item" %>
                  <%= link_to "Log Out", destroy_user_session_path, method: :delete, class:"navbar-item" %>
                </div>
              </div>
            <% else %>

            <%= link_to "Sign In", new_user_session_path, class:"navbar-item button is-warning" %>
            <%= link_to "Sign up", new_user_registration_path, class:"navbar-item button is-warning"%>

            <% end %>

            </div>
          </div>
        </div>
    </div>
  </nav>

  <%= yield(:header) %>

  <div class="container">

    <%= yield %>

  </div>

  <script>
    // Auto-hide flash messages after 3 seconds
    setTimeout(function() {
      const notifications = document.querySelectorAll('.global-notification');
      notifications.forEach(function(notification) {
        notification.style.display = 'none';
      });
    }, 3000);
  </script>
  </body>
</html>
