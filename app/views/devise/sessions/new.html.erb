<section class="section">
	<div class="container">
		<div class="columns is-centered">
			<div class="column is-4">
				<h2 class="title is-2">Log in</h2>
				<%= simple_form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>

				  <div class="field">
				  	<div class="control">
				    	<%= f.input :email, required: false, input_html: { class: "input"}, wrapper: false, label_html: { class: "label" } %>
				    </div>
				  </div>

				  <div class="field">
				  	<div class="control">
				    <%= f.input :password, required: false, input_html: { class: "input"}, wrapper: false, label_html: { class: "label" } %>
						</div>
					</div>

					<div class="field">
				  	<div class="control">
				    <%= f.input :remember_me, wrapper: false, as: :boolean if devise_mapping.rememberable? %>
				  	</div>
					</div>

				  <%= f.button :submit, "Log in", class:"button is-outlined is-medium" %>
				<% end %>
				<br/>
				<%= render "devise/shared/links" %>
			</div>
		</div>
	</div>
</section>