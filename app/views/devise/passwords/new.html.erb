<section class="section">
  <div class="container">
    <div class="columns is-centered">
      <div class="column is-4">
        
				<h2 class="title is-2">Forgot your password?</h2>

				<%= simple_form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post }) do |f| %>
				  <%= f.error_notification %>

					<div class="field">
            <div class="control">
				    <%= f.input :email, required: true, autofocus: true, input_html: { class: "input"}, wrapper: false, label_html: { class: "label" } %>
				  	</div>
					</div>
				  
				  <%= f.button :submit, "Send me reset password instructions", class:"button is-outlined" %>
				  
				<% end %>
				<br />
				<%= render "devise/shared/links" %>

    </div>
    </div>
  </div>
</section>