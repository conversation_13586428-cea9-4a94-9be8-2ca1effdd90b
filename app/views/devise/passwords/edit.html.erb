<section class="section">
  <div class="container">
    <div class="columns is-centered">
      <div class="column is-4">

        <h2 class="title is-2">Change your password</h2>

        <%= simple_form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f| %>
          <%= f.error_notification %>

          <%= f.input :reset_password_token, as: :hidden %>
          <%= f.full_error :reset_password_token %>

          <div class="field">
            <div class="control">
            <%= f.input :password, label: "New password", required: true, autofocus: true, input_html: { class: "input"}, wrapper: false, label_html: { class: "label" }, hint: ("#{@minimum_password_length} characters minimum" if @minimum_password_length) %>
            </div>
          </div>

          <div class="field">
            <div class="control">
            <%= f.input :password_confirmation, label: "Confirm your new password", input_html: { class: "input"}, wrapper: false, label_html: { class: "label" }, required: true %>
            </div>
          </div>

          
            <%= f.button :submit, "Change my password", class:"button is-outlined" %>

        <% end %>

        <%= render "devise/shared/links" %>

      </div>
    </div>
  </div>
</section>