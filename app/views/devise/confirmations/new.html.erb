<section class="section">
  <div class="columns is-centered">
    <div class="column is-4">
		<h2 class="title is-2">Resend confirmation instructions</h2>

		<%= simple_form_for(resource, as: resource_name, url: confirmation_path(resource_name), html: { method: :post }) do |f| %>
		  <%= f.error_notification %>
		  <%= f.full_error :confirmation_token %>


		   <div class="field">
        <div class="control">
		    <%= f.input :email, required: true, input_html: { class: "input"}, wrapper: false, label_html: { class: "label" } %>
		  	</div>
			</div>

		  <div class="form-actions">
		    <%= f.button :submit, "Resend confirmation instructions", class:'button is-warning' %>
		  </div>
		<% end %>

		<%= render "devise/shared/links" %>
	</div>
	</div>
</section>
