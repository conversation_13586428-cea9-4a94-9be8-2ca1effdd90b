<<% content_for :header do %>
<section class="hero is-primary">
  <div class="hero-body">
    <div class="container">
      <h1 class="title">
        Welcome to Shop
      </h1>
      <h2 class="subtitle">
        Your one-stop e-commerce destination
      </h2>
      <% unless user_signed_in? %>
        <%= link_to "Get Started", new_user_registration_path, class: "button is-white is-large" %>
      <% end %>
    </div>
  </div>
</section>
<% end %>

<div class="section">
  <div class="container">
    <h2 class="title is-3">Latest Products</h2>

    <% if @products.any? %>
      <div class="product-index-grid pt4">
        <% @products.each do |product| %>
          <div class="product border-light">
            <div class="product-thumb">
              <%= link_to image_tag(product.image_url(:thumb)), product %>
              <% if product.condition? %>
                <div class="condition">
                  <span class="tag is-dark"><%= product.condition %></span>
                </div>
              <% end %>
            </div>

            <div class="pa3">
              <h3 class="fw7 f4 title"><%= link_to product.title, product %></h3>
              <p class="has-text-gray fg pt1">Sold by: <%= product_author(product) %></p>
              <p class="f3 fw6 has-text-right pt2 price"><%= number_to_currency(product.price) %></p>

              <div class="product-actions pt2">
                <%= form_with url: cart_add_item_path, method: :post, local: true do |form| %>
                  <%= form.hidden_field :product_id, value: product.id %>
                  <%= form.submit "Add to Cart", class: "button is-primary is-small" %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <div class="has-text-centered pt4">
        <%= link_to "View All Products", products_path, class: "button is-primary is-large" %>
      </div>
    <% else %>
      <div class="has-text-centered">
        <p class="title is-4">No products available yet</p>
        <% if user_signed_in? %>
          <p class="subtitle">Be the first to <%= link_to "sell something", new_product_path %>!</p>
        <% else %>
          <p class="subtitle"><%= link_to "Sign up", new_user_registration_path %> to start selling!</p>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
