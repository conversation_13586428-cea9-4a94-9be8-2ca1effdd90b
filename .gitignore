# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep

/public/assets
.byebug_history

# Ignore master key for decrypting credentials and more.
/config/master.key

/public/packs
/public/packs-test
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity

# Ignore application configuration
/config/application.yml

# Database files
*.sqlite3
*.sqlite3-*
/db/*.sqlite3
/db/*.sqlite3-journal
/db/*.sqlite3-*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignore uploaded files and user content
/public/uploads
/public/system
/storage

# Ignore CarrierWave uploads
/public/uploads/*

# Ignore temporary files
*.tmp
*.temp
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Ignore IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Ignore OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Ignore RubyMine files
.idea/

# Ignore Vim files
*.swp
*.swo

# Ignore Emacs files
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Ignore Sublime Text files
*.sublime-project
*.sublime-workspace

# Ignore TextMate files
*.tmproj
*.tmproject
tmtags

# Ignore coverage reports
/coverage/

# Ignore simplecov generated files
coverage/

# Ignore RSpec generated files
/spec/tmp

# Ignore Capybara screenshots
/tmp/capybara

# Ignore Spring files
/spring/*.pid

# Ignore Webpacker cache
/public/packs
/public/packs-test

# Ignore yarn files
yarn-error.log
yarn-debug.log*
.yarn-integrity

# Ignore node modules
node_modules/

# Ignore package-lock.json (use yarn.lock instead)
package-lock.json

# Ignore credentials and secrets
/config/credentials.yml.enc
/config/master.key
/config/secrets.yml

# Ignore Stripe and payment credentials
.stripe_*

# Ignore local development files
.env.development
.env.test
.env.production

# Ignore backup files
*.bak
*.backup

# Ignore log files
*.log

# Ignore cache files
.sass-cache/
.cache/

# Ignore vendor bundle (if using bundle install --path vendor/bundle)
/vendor/bundle

# Ignore rbenv files
.ruby-version
.ruby-gemset
.bundle/
# Ignore RVM files
.rvmrc

# Ignore pow files
.powrc
.powder

# Ignore Vagrant files
.vagrant/

# Ignore Docker files (if not needed in repo)
# Docker
.dockerignore
.docker-compose.override.yml
.docker-compose.override.yaml


# Ignore test files and fixtures that shouldn't be committed
/test/fixtures/files/*
!/test/fixtures/files/.keep

# Ignore development database
/db/development.sqlite3
/db/development.sqlite3-*

# Ignore test database
/db/test.sqlite3
/db/test.sqlite3-*

# Ignore production database (if SQLite)
/db/production.sqlite3
/db/production.sqlite3-*

# Ignore schema.rb if using structure.sql
# /db/schema.rb

# Ignore seeds that contain sensitive data
# /db/seeds/

# Ignore any custom scripts or notes
/scripts/
/notes/
TODO.md
NOTES.md

# Ignore any downloaded zip files
*.zip

# Ignore any generated documentation
/doc/
/rdoc/

# Ignore any generated API documentation
/public/doc/

# Ignore any generated coverage reports
/coverage/

# Ignore any profiling data
/tmp/profiling/

# Ignore any benchmark data
/tmp/benchmarks/

# Ignore any custom configuration files that might contain secrets
config/local_env.yml
config/settings.local.yml
config/settings/*.local.yml
config/environments/*.local.yml

log/
tmp/
storage/
vendor/
